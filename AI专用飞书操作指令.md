# AI专用飞书多维表格操作指令

## 🎯 任务目标
通过飞书开放平台API向多维表格新增记录

## 🔑 关键配置（直接使用）
```python
APP_ID = "cli_a828491ea031d013"
APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
APP_TOKEN = "TtULb7pBiaGRMgs4dfac4aLAnId"
TABLE_ID = "tblzpzFoRcDR3PC3"
BASE_URL = "https://open.feishu.cn/open-apis"
```

## 📊 表格字段（必须完全匹配）
```python
# 8个字段，字段名必须完全一致：
fields = {
    "发送时间": int(datetime.now().timestamp() * 1000),  # 毫秒时间戳！
    "发送人": "用户名",                                    # 文本
    "群聊名称": "群聊名称",                                # 文本
    "消息内容": "消息内容",                                # 文本
    "消息类型": "文本消息",                                # 文本
    "消息ID": "msg_123456",                              # 文本
    "被引用消息ID": ""                                    # 文本，可为空
    # 消息附件字段暂不使用
}
```

## 🚀 核心代码模板（直接复制使用）

```python
import subprocess
import json
from datetime import datetime

def execute_curl(method, url, headers=None, data=None):
    """执行curl命令 - 必须使用此方法避免网络问题"""
    cmd = ['curl', '-X', method, url, '--insecure', '--silent']
    
    if headers:
        for k, v in headers.items():
            cmd.extend(['-H', f'{k}: {v}'])
    if data:
        cmd.extend(['-d', data])
    
    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, encoding='utf-8', errors='ignore')
    return json.loads(result.stdout)

def get_access_token():
    """获取访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = json.dumps({
        "app_id": "cli_a828491ea031d013",
        "app_secret": "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
    })
    headers = {'Content-Type': 'application/json'}
    
    result = execute_curl('POST', url, headers, data)
    return result['tenant_access_token']

def add_record(sender, content, group_name="默认群聊", message_type="文本消息", quoted_id=""):
    """添加一条记录到飞书多维表格"""
    token = get_access_token()
    
    # 准备数据 - 字段名必须完全匹配！
    fields = {
        "发送时间": int(datetime.now().timestamp() * 1000),  # 注意：毫秒时间戳
        "发送人": sender,
        "群聊名称": group_name,
        "消息内容": content,
        "消息类型": message_type,
        "消息ID": f"msg_{int(datetime.now().timestamp())}",
        "被引用消息ID": quoted_id
    }
    
    # API调用
    url = "https://open.feishu.cn/open-apis/bitable/v1/apps/TtULb7pBiaGRMgs4dfac4aLAnId/tables/tblzpzFoRcDR3PC3/records"
    data = json.dumps({"fields": fields})
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    result = execute_curl('POST', url, headers, data)
    
    if result.get('code') == 0:
        record_id = result['data']['record']['record_id']
        print(f"✅ 成功添加记录: {record_id}")
        return record_id
    else:
        raise Exception(f"添加失败: {result.get('msg')}")

def add_batch_records(records_data):
    """批量添加记录"""
    token = get_access_token()
    
    # 构造批量数据
    records = []
    for record in records_data:
        records.append({"fields": record})
    
    url = "https://open.feishu.cn/open-apis/bitable/v1/apps/TtULb7pBiaGRMgs4dfac4aLAnId/tables/tblzpzFoRcDR3PC3/records/batch_create"
    data = json.dumps({"records": records})
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    result = execute_curl('POST', url, headers, data)
    
    if result.get('code') == 0:
        record_ids = [r['record_id'] for r in result['data']['records']]
        print(f"✅ 成功批量添加 {len(record_ids)} 条记录")
        return record_ids
    else:
        raise Exception(f"批量添加失败: {result.get('msg')}")
```

## 📝 使用示例

### 添加单条记录
```python
# 添加一条简单消息
record_id = add_record("张三", "这是一条测试消息", "技术讨论群")

# 添加回复消息
record_id = add_record("李四", "我同意这个方案", "技术讨论群", "回复消息", "msg_123456")
```

### 批量添加记录
```python
# 准备多条记录数据
records = []
base_time = datetime.now()

for i in range(3):
    record = {
        "发送时间": int((base_time + timedelta(minutes=i)).timestamp() * 1000),
        "发送人": f"用户{i+1}",
        "群聊名称": "测试群聊",
        "消息内容": f"这是第{i+1}条消息",
        "消息类型": "文本消息",
        "消息ID": f"msg_{int(datetime.now().timestamp())}_{i}",
        "被引用消息ID": ""
    }
    records.append(record)

# 批量添加
record_ids = add_batch_records(records)
```

## ⚠️ 重要注意事项

### 1. 时间戳格式
```python
# 正确 ✅
timestamp = int(datetime.now().timestamp() * 1000)  # 毫秒时间戳

# 错误 ❌
timestamp = int(datetime.now().timestamp())  # 秒时间戳
```

### 2. 字段名匹配
字段名必须与表格中完全一致，包括：
- 大小写
- 空格
- 特殊字符

### 3. 网络方案
必须使用curl方案，不要使用requests：
```python
# 正确 ✅
result = execute_curl('POST', url, headers, data)

# 错误 ❌ - 会有网络问题
response = requests.post(url, headers=headers, json=data)
```

### 4. 错误处理
```python
try:
    record_id = add_record("用户", "消息")
    print(f"成功: {record_id}")
except Exception as e:
    print(f"失败: {str(e)}")
```

## 🧪 快速测试

```python
def test_api():
    """快速测试API连接和添加功能"""
    try:
        # 测试添加记录
        record_id = add_record("测试用户", "API连接测试消息")
        print(f"✅ 测试成功，记录ID: {record_id}")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

# 运行测试
test_api()
```

## 🔧 常见错误解决

| 错误信息 | 原因 | 解决方案 |
|---------|------|----------|
| `permission denied` | 权限不足 | 检查应用是否添加到表格 |
| `invalid param` | 字段名错误 | 确认字段名完全匹配 |
| `field type mismatch` | 数据类型错误 | 检查时间戳格式（毫秒） |
| `SSL Error` | 网络连接问题 | 使用curl + `--insecure` |

## 🎯 完整工作流程

1. **复制核心代码** - 直接使用上面的代码模板
2. **准备数据** - 按照字段格式准备记录数据
3. **调用函数** - 使用`add_record()`或`add_batch_records()`
4. **检查结果** - 查看返回的record_id确认成功
5. **验证数据** - 在飞书表格中确认记录已添加

## 📋 字段数据示例

```python
# 完整的记录数据示例
example_record = {
    "发送时间": 1703123456789,           # 毫秒时间戳
    "发送人": "张三",                     # 发送消息的用户
    "群聊名称": "技术讨论群",              # 群聊或频道名称
    "消息内容": "项目进展如何？",          # 消息的具体内容
    "消息类型": "文本消息",               # 消息类型标识
    "消息ID": "msg_1703123456",          # 消息唯一标识
    "被引用消息ID": ""                   # 回复消息时填写被回复的消息ID
}
```

---

**使用说明**: 直接复制上述代码模板，根据需要修改参数即可快速实现飞书多维表格记录添加功能。所有配置信息已预设完成，无需额外配置。
