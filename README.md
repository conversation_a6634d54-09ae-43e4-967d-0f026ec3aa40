# 微信群消息监控与Coze智能回复系统

一个基于微信RPC和Coze API的智能群聊机器人，可以监控微信群消息并提供智能回复。

## 功能特点

- ✅ **实时监控**: 监控微信群实时消息，不处理历史消息
- ✅ **智能触发**: 只有包含特定触发词的消息才会触发回复
- ✅ **消息预处理**: 自动移除@机器人部分，避免对AI回复的干扰
- ✅ **Coze集成**: 调用Coze API生成智能回复
- ✅ **静默启动**: 启动时不发送任何消息到微信

## 使用方法

### 1. 启动程序
```bash
python wechat_bot.py
```

### 2. 触发回复
在微信群中发送包含以下触发词的消息：
```
@我来收集总结你们的聊天(bot)
```

支持的消息格式：
- `@我来收集总结你们的聊天(bot)`
- `@我来收集总结你们的聊天(bot) 你好`
- `请@我来收集总结你们的聊天(bot)帮忙总结`

### 3. 停止程序
按 `Ctrl+C` 停止监控

## 配置说明

程序中的关键配置项：

```python
# 微信RPC配置
WECHAT_RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "22504"

# Coze API配置
COZE_TOKEN = "your_coze_token"
COZE_BOT_ID = "your_bot_id"
```

## 工作原理

1. **消息监控**: 程序连接微信数据库，实时监控群消息
2. **触发检测**: 检查消息是否包含触发词
3. **消息预处理**: 移除@机器人部分，提取实际消息内容
4. **AI回复**: 调用Coze API生成智能回复
5. **发送回复**: 将回复发送到相应的群聊

## 示例

用户发送：
```
@我来收集总结你们的聊天(bot) 请总结今天的讨论内容
```

系统处理：
1. 检测到触发词，决定回复
2. 提取实际消息：`请总结今天的讨论内容`
3. 发送给Coze API
4. 获取AI回复并发送到群聊

## 注意事项

- 确保微信RPC服务正常运行
- 确保Coze API配置正确
- 程序启动时会跳过所有历史消息，只监控实时消息
- 只有包含完整触发词的消息才会触发回复
